import '../../testing/middlewareMock';

import { v4 } from 'uuid';

import { DebitCardAccountCommandService } from '../../../service/debitCardAccount/debitCardAccountCommandService';
import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

import { handler } from './createDebitCardAccountHandler';

jest.mock('../../../service/debitCardAccount/debitCardAccountCommandService');

const mockContext = {} as any;
const mockRequest = {} as any;

const MockDebitCardAccountCommandService = <jest.Mock<DebitCardAccountCommandService>>DebitCardAccountCommandService;
const mockDebitCardAccountCommandService = <jest.Mocked<DebitCardAccountCommandService>>(
  MockDebitCardAccountCommandService.mock.instances[0]
);

it('should be able to handle createDebitCardAccount', async () => {
  const event = getBaseAppSyncResolverEvent({
    args: {
      input: {
        entityUuid: v4(),
        name: v4(),
        icon: {},
      },
    },
    request: mockRequest,
  });
  await handler(event, mockContext, () => {});

  expect(mockDebitCardAccountCommandService.createAccount).toHaveBeenCalledTimes(1);
});
