import type { Config } from 'jest';

// eslint-disable-next-line import/no-relative-packages
import commonConfig from '../../../../jest.preset';

const config: Config = {
  roots: ['<rootDir>/tests'],
  testTimeout: 30000,
  collectCoverage: false,
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  reporters: [
    ...(commonConfig.reporters ?? []),
    ['summary', { summaryThreshold: 1 }],
    ['github-actions', { silent: false }], // remove default for github actions
    [
      'jest-gh-md-reporter',
      {
        filename: 'test-report.md',
        publicPath: 'dist/',
        name: 'Integration Test Report',
        collapseSuites: true,
        includeConsoleOutput: true,
        detailedReport: true,
      },
    ],
  ],
};

export default config;
